<template>
  <div class="order-recycle-bin">
    <div class="order-recycle-bin-tip">
      已删除订单无法申请售后，如需操作请先还原订单
    </div>
    <div class="order-recycle-bin-content" ref="contentRef">
      <!-- 骨架屏 -->
      <div v-if="showSkeleton" class="skeleton-container">
        <div v-for="i in 3" :key="`skeleton-${i}`" class="order-item">
          <WoCard>
            <div class="skeleton-order">
              <!-- 订单头部骨架 -->
              <div class="skeleton-header">
                <div class="skeleton-order-number"></div>
                <div class="skeleton-status"></div>
              </div>

              <!-- 商品信息骨架 -->
              <div class="skeleton-goods">
                <div class="skeleton-image"></div>
                <div class="skeleton-content">
                  <div class="skeleton-title"></div>
                  <div class="skeleton-subtitle"></div>
                  <div class="skeleton-price"></div>
                </div>
              </div>

              <!-- 按钮骨架 -->
              <div class="skeleton-buttons">
                <div class="skeleton-button"></div>
                <div class="skeleton-button"></div>
              </div>
            </div>
          </WoCard>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!showSkeleton && !loading && orderList.length === 0 && finished" class="empty-state">
        <div class="empty-content">
          <img src="../assets/no-data.png" alt="暂无订单" class="empty-image" />
        </div>
      </div>

      <!-- 订单列表 -->
      <van-list v-else v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad"
        :immediate-check="false">
        <div v-for="order in orderList" :key="order.id" class="order-item" :class="{ 'deleting': order.isDeleting }">
          <WoCard>
            <!-- 订单头部信息 -->
            <div class="order-header">
              <div class="order-number-container">
                <span class="order-number-text">订单号：{{ order.id }}</span>
                <img src="@/static/images/copy.png" alt="复制" class="copy-icon"
                  @click.stop="copyOrderNumber(order.id)" />
              </div>
              <div class="order-status" :class="order.statusClass">{{ order.statusText }}</div>
            </div>

            <!-- 商品列表 -->
            <div class="goods-section">
              <!-- 多商品订单：显示为一个整体 -->
              <OrderGoodsCard :key="order.id" :item="order" :image-size="75" :min-height="110" :showActions="true"
                :itemId="order.id" :showMore="false" @click="onDetailClick(order)">
                <template #actions>
                  <WoButton v-for="button in getVisibleButtons(order)" :key="button.text"
                    :type="getButtonType(button.color)" size="small" @click="button.handler">
                    {{ button.text }}
                  </WoButton>
                </template>
              </OrderGoodsCard>
            </div>
          </WoCard>
        </div>
      </van-list>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick, onActivated, onDeactivated } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import OrderGoodsCard from '@components/Common/OrderGoodsCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import useClipboard from 'vue-clipboard3'
import { showLoadingToast, showToast, closeToast } from 'vant'
import { getOrderRecycleBinList, modOrderListShow } from '@/api'
import { getBizCode } from '@utils/curEnv.js'
import { useAlert } from '@/hooks/index.js'
import {useRouter} from "vue-router";

const { toClipboard } = useClipboard()
const $alert = useAlert()
const router = useRouter()
// 保存标签页的滚动位置
const scrollPositions = ref({
  all: 0
})

// 更新滚动位置
const updateScrollPosition = (position) => {
  scrollPositions.value.all = position
}



const contentRef = ref(null)
const loading = ref(false)
const finished = ref(false)
const orderList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const showSkeleton = ref(true)
const onLoadGetDataLock = ref(false)
const totalPage = ref(0)
const error = ref(false)
const finishedText = ref('没有更多了')

// 监听滚动事件，更新滚动位置
const handleScroll = () => {
  if (contentRef.value) {
    const scrollTop = contentRef.value.scrollTop || document.documentElement.scrollTop || document.body.scrollTop
    updateScrollPosition(scrollTop)
  }
}

// 复制订单号
const copyOrderNumber = async (orderNumber) => {
  try {
    await toClipboard(orderNumber);
    showToast('复制成功');
  } catch (e) {
    console.error(e);
    showToast('复制失败');
  }
}

// 订单详情点击处理
const onDetailClick = (order) => {
  router.push({
    path: '/user/order/detail',
    query: {
      orderId: order.id,
      isPay: order.orderState === '0' ? '1' : '2'
    }
  })
}


// 获取可见按钮
const getVisibleButtons = (order) => {
  return [
    {
      text: '永久删除',
      color: 'default',
      handler: () => handlePermanentDelete(order)
    },
    {
      text: '还原订单',
      color: 'primary',
      handler: () => handleRestoreOrder(order)
    }
  ]
}

// 获取按钮类型
const getButtonType = (color) => {
  const typeMap = {
    primary: 'gradient',
    default: 'default'
  }
  return typeMap[color] || 'default'
}

// 永久删除订单
const handlePermanentDelete = async (order) => {
  try {
     // 确认删除后执行删除操作
     const cancelOrderFn = async () => {
      showLoadingToast()
      const params = {
        supplierOrderId: order.id,
        isDelete: 2
      }
      const [err] = await modOrderListShow(params)
      closeToast()

      if (!err) {
        showToast('订单删除成功!')
        const index = orderList.value.findIndex(item => item.id === order.id)
        if (index !== -1) {
          // 添加删除动画标识
          orderList.value[index].isDeleting = true
          setTimeout(() => {
            orderList.value.splice(index, 1)
            if (orderList.value.length === 0) {
              finishedText.value = ''
            }
          }, 500) // 动画持续时间
        }
      } else {
        showToast(err.msg || '删除失败')
      }
    }

    $alert({
      title: '',
      message: '永久删除后，您将无法查看及还原订单，确认继续删除吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      showCancelButton: true,
      onConfirmCallback: async () => {
        await cancelOrderFn()
      }
    })
  } catch (error) {
    // 用户取消删除或其他错误
    if (error !== 'cancel') {
      showToast('删除失败')
    }
  }
}

// 还原订单
const handleRestoreOrder = async (order) => {
  try {
    showLoadingToast()
    const params = {
      supplierOrderId: order.id,
      isDelete: 0
    }
    const [err] = await modOrderListShow(params)
    closeToast()

    if (!err) {
      showToast('订单已成功还原至"我的订单"')
      // 设置sessionStorage标识
      window.sessionStorage.setItem('woRecycleBinRestore', 1)

      const index = orderList.value.findIndex(item => item.id === order.id)
      if (index !== -1) {
        // 添加删除动画标识
        orderList.value[index].isDeleting = true
        setTimeout(() => {
          orderList.value.splice(index, 1)
          if (orderList.value.length === 0) {
            finishedText.value = ''
          }
        }, 500) // 动画持续时间
      }
    } else {
      showToast(err.msg || '还原失败')
    }
  } catch (error) {
    closeToast()
    showToast('还原失败')
  }
}

// 加载数据
const onLoad = async () => {
  if (onLoadGetDataLock.value) {
    return
  }

  const params = {
    bizCode: getBizCode('ORDER'),
    pageNo: currentPage.value,
    pageSize: pageSize.value
  }

  onLoadGetDataLock.value = true

  const [err, json] = await getOrderRecycleBinList(params)

  onLoadGetDataLock.value = false
  loading.value = false
  showSkeleton.value = false

  if (!err) {
    currentPage.value++

    if (json?.list.length > 0) {
      finishedText.value = '没有更多了'
    }

    // 如果没有数据
    if (json && json?.list.length <= 0) {
      finished.value = true
      return
    }

    orderList.value = [...orderList.value, ...json.list]

    // 当前列表有多少页
    totalPage.value = json.totalPage
    if (currentPage.value > totalPage.value) {
      finished.value = true
    }

    // 恢复滚动位置
    if (currentPage.value === 2 && scrollPositions.value.all > 0) {
      nextTick(() => {
        if (contentRef.value) {
          contentRef.value.scrollTop = scrollPositions.value.all
        }
      })
    }
  } else {
    error.value = true
    showToast(err.msg || '获取回收站数据失败')
  }
}


// 组件挂载时
onMounted(() => {
  // 添加滚动事件监听
  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', handleScroll)
  } else {
    window.addEventListener('scroll', handleScroll)
  }

  // 初始加载数据
  onLoad()
})

// 组件卸载时
onUnmounted(() => {
  // 移除滚动事件监听
  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', handleScroll)
  } else {
    window.removeEventListener('scroll', handleScroll)
  }
})

// 页面激活时触发
onActivated(() => {
  // 页面重新显示时，恢复滚动位置
  console.log('回收站页面激活')
  nextTick(() => {
    if (contentRef.value && scrollPositions.value.all > 0) {
      contentRef.value.scrollTop = scrollPositions.value.all
    }
  })
})

// 页面失活时触发
onDeactivated(() => {
  console.log('回收站页面失活')
  // 保存当前滚动位置
  if (contentRef.value) {
    scrollPositions.value.all = contentRef.value.scrollTop
  }
})
</script>

<style scoped lang="less">
.order-recycle-bin {
  height: 100vh;
  display: flex;
  flex-direction: column;

  .order-recycle-bin-tip {
    background: #FEFAE9;
    color: #EE9035;
    padding: 10px 22px;
    font-size: @font-size-12;
    box-sizing: border-box;
  }

  .order-recycle-bin-content {
    flex: 1;
    overflow: auto;
    background-color: #f5f5f5;
    padding: 10px;
  }

  .order-item {
    margin-bottom: 10px;
    transition: all 0.5s ease;

    &.deleting {
      opacity: 0;
      transform: translateX(-100%);
    }
  }

  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;

    .order-shop {
      font-size: @font-size-14;
      font-weight: bold;
      color: @text-color-primary;
    }

    .order-number-container {
      display: flex;
      align-items: center;
      margin-right: 15px;
      width: 100%;
      overflow: hidden;

      .order-number-text {
        font-size: @font-size-11;
        color: @text-color-secondary;
        margin-right: 3px;
        .ellipsis()
      }

      .copy-icon {
        width: 10px;
        height: 10px;
        cursor: pointer;
      }
    }

    .order-status {
      flex-shrink: 0;
      font-size: @font-size-14;
      font-weight: @font-weight-600;

      &.status-unpaid {
        color: @theme-color;
      }

      &.status-unshipped {
        color: #2196f3;
      }

      &.status-shipped {
        color: #4caf50;
      }

      &.status-completed {
        color: @text-color-secondary;
      }
    }
  }

  .goods-section {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 骨架屏样式
  .skeleton-container {
    .skeleton-order {
      padding: 15px;

      .skeleton-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .skeleton-order-number {
          width: 120px;
          height: 14px;
          background: #f0f0f0;
          border-radius: 4px;
        }

        .skeleton-status {
          width: 60px;
          height: 14px;
          background: #f0f0f0;
          border-radius: 4px;
        }
      }

      .skeleton-goods {
        display: flex;
        margin-bottom: 15px;

        .skeleton-image {
          width: 75px;
          height: 75px;
          background: #f0f0f0;
          border-radius: 8px;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .skeleton-content {
          flex: 1;

          .skeleton-title {
            width: 80%;
            height: 16px;
            background: #f0f0f0;
            border-radius: 4px;
            margin-bottom: 8px;
          }

          .skeleton-subtitle {
            width: 60%;
            height: 14px;
            background: #f0f0f0;
            border-radius: 4px;
            margin-bottom: 8px;
          }

          .skeleton-price {
            width: 40%;
            height: 14px;
            background: #f0f0f0;
            border-radius: 4px;
          }
        }
      }

      .skeleton-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 10px;

        .skeleton-button {
          width: 60px;
          height: 28px;
          background: #f0f0f0;
          border-radius: 4px;
        }
      }
    }
  }

  // 空状态样式
  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;

    .empty-content {
      text-align: center;

      .empty-image {
        width: 120px;
        height: 120px;
        opacity: 0.6;
      }
    }
  }
}
</style>
