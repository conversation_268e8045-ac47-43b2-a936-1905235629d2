import Alert from './Alert.vue'
import { createVNode, render } from 'vue'

function createInstance(options) {
  // 创建容器
  const container = document.createElement('div')
  document.body.appendChild(container)

  // 用于存储回调函数的对象
  const callbacks = {
    onConfirm: null,
    onCancel: null
  }

  // 创建vnode，传入回调处理函数
  const vnode = createVNode(Alert, {
    ...options,
    onConfirm: () => {
      if (callbacks.onConfirm) {
        callbacks.onConfirm()
      }
    },
    onCancel: () => {
      if (callbacks.onCancel) {
        callbacks.onCancel()
      }
    }
  })

  // 渲染vnode
  render(vnode, container)

  // 获取组件实例
  const instance = vnode.component.exposed

  // 添加销毁方法
  instance.destroy = () => {
    render(null, container)
    if (document.body.contains(container)) {
      document.body.removeChild(container)
    }
  }

  // 添加设置回调的方法
  instance.setCallbacks = (onConfirm, onCancel) => {
    callbacks.onConfirm = onConfirm
    callbacks.onCancel = onCancel
  }

  return instance
}

function alert(options) {
  if (typeof options === 'string') {
    options = { message: options }
  }

  // 提取用户传入的回调函数
  const userOnConfirm = options.onConfirmCallback
  const userOnCancel = options.onCancelCallback

  // 创建实例
  const instance = createInstance(options)

  // 返回Promise，同时支持回调函数
  return new Promise((resolve, reject) => {
    // 设置内部回调处理
    instance.setCallbacks(
      () => {
        // 先执行用户传入的回调
        if (userOnConfirm) {
          try {
            userOnConfirm()
          } catch (error) {
            console.error('Error in onConfirmCallback:', error)
          }
        }

        // 然后resolve Promise
        resolve('confirmed')

        // 关闭并销毁
        instance.close()
        setTimeout(() => {
          instance.destroy()
        }, 300)
      },
      () => {
        // 先执行用户传入的回调
        if (userOnCancel) {
          try {
            userOnCancel()
          } catch (error) {
            console.error('Error in onCancelCallback:', error)
          }
        }

        // 然后reject Promise
        reject(new Error('User cancelled the dialog'))

        // 关闭并销毁
        instance.close()
        setTimeout(() => {
          instance.destroy()
        }, 300)
      }
    )

    // 显示弹窗
    instance.show()
  })
}

// 插件安装方法
const install = (app) => {
  app.component(Alert.name, Alert)
  app.config.globalProperties.$alert = alert
}

// 导出插件对象
export default {
  install
}

// 导出组件和方法，方便按需引入
export { Alert, alert }
